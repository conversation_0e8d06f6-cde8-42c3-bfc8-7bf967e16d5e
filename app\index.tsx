import { useEffect, useState } from "react";
import { useRouter } from "expo-router";
import { RootState, useAppSelector } from "@/store/store";
import { LoadingOverlay, LocationPermissionModal } from "@/components";
import { useStoreReady } from "@/hooks/useStoreReady";

export default function IndexScreen() {
  const router = useRouter();
  const token = useAppSelector((state: RootState) => state.auth.token);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [hasNavigated, setHasNavigated] = useState(false);
  const isStoreReady = useStoreReady();

  useEffect(() => {
    if (hasNavigated || !isStoreReady) return;

    // Add a small delay to ensure the layout is fully mounted
    const timer = setTimeout(() => {
      if (!token) {
        router.replace("/(auth)/intro");
      } else {
        router.replace("/(protected)/(tabs)/home");
      }
      setHasNavigated(true);
    }, 200);

    return () => clearTimeout(timer);
  }, [token, hasNavigated, isStoreReady]);

  const handleLocationPermissionComplete = async () => {
    setShowLocationModal(false);
  };

  return (
    <>
      <LoadingOverlay isLoading={!isStoreReady} size="large" />
      <LocationPermissionModal
        isVisible={showLocationModal}
        onComplete={handleLocationPermissionComplete}
      />
    </>
  );
}
