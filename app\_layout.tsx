import { Slot } from "expo-router";
import { Provider } from "react-redux";
import { store, persistor } from "@/store/store";
import {
  LoadingOverlay,
  ErrorBoundary,
  toastConfig,
  CustomSplashScreen,
} from "@/components";
import Toast from "react-native-toast-message";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet";
import { PersistGate } from "redux-persist/integration/react";
import { ThemeProvider } from "styled-components/native";
import { useTheme } from "@/hooks/useTheme";
import "@/services/setupInterceptors";
import { SafeAreaView, StatusBar } from "react-native";
import * as SplashScreen from "expo-splash-screen";
import { useState, useEffect } from "react";

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const { theme, isDark } = useTheme();
  const [appIsReady, setAppIsReady] = useState(false);

  useEffect(() => {
    const prepare = async () => {
      try {
        // Wait for splash screen to hide
        await SplashScreen.hideAsync();
        // Add a small delay to ensure everything is properly initialized
        await new Promise((resolve) => setTimeout(resolve, 100));
        setAppIsReady(true);
      } catch (error) {
        console.warn('Error preparing app:', error);
        setAppIsReady(true);
      }
    };
    prepare();
  }, []);

  if (!appIsReady) {
    return <CustomSplashScreen />;
  }

  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <PersistGate
          loading={<LoadingOverlay isLoading={true} size="large" />}
          persistor={persistor}
        >
          <SafeAreaView style={{ flex: 1 }}>
            <StatusBar
              backgroundColor={theme.colors.background}
              barStyle={isDark ? "light-content" : "dark-content"}
            />
            <GestureHandlerRootView style={{ flex: 1 }}>
              <BottomSheetModalProvider>
                <ErrorBoundary>
                  <Slot />
                  <Toast config={toastConfig} />
                </ErrorBoundary>
              </BottomSheetModalProvider>
            </GestureHandlerRootView>
          </SafeAreaView>
        </PersistGate>
      </ThemeProvider>
    </Provider>
  );
}
