import { Image } from "react-native";
import { useFocusEffect, useRouter } from "expo-router";
import {
  Container,
  ContentContainer,
  TopContainer,
  LogoContainer,
  MiddleContainer,
  Question,
  ButtonsContainer,
  RoleButton,
  RoleButtonText,
  LinkButton,
  LinkText,
} from "@/styles/Intro.styles";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "@/store/store";
import { resetUser, setUser } from "@/store/slices/authSlice";
import { UserType } from "@/types/api";
import { useCallback, useState } from "react";

export default function IntroScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [selectedRole, setSelectedRole] = useState<number | null>(null);

  useFocusEffect(
    useCallback(() => {
      dispatch(resetUser());
      setSelectedRole(null);
    }, [dispatch])
  );

  const handleRoleSelect = (role_id: number) => {
    setSelectedRole(role_id);
    dispatch(setUser({ role_id: role_id }));
    router.push("/(auth)/register");
  };

  return (
    <Container>
      <ContentContainer>
        <TopContainer>
          <LogoContainer>
            <Image
              source={require("../../assets/icon.png")}
              style={{ height: "100%", width: "100%" }}
              resizeMode="contain"
            />
          </LogoContainer>
        </TopContainer>

        <MiddleContainer>
          <Question>{t("intro.choose_role")}</Question>
          <ButtonsContainer>
            <RoleButton
              isSelected={selectedRole === UserType.CUSTOMER}
              onPress={() => handleRoleSelect(UserType.CUSTOMER)}
            >
              <RoleButtonText isSelected={selectedRole === UserType.CUSTOMER}>
                {t("intro.customer_button")}
              </RoleButtonText>
            </RoleButton>
            <RoleButton
              isSelected={selectedRole === UserType.VENDOR}
              onPress={() => handleRoleSelect(UserType.VENDOR)}
            >
              <RoleButtonText isSelected={selectedRole === UserType.VENDOR}>
                {t("intro.vendor_button")}
              </RoleButtonText>
            </RoleButton>
          </ButtonsContainer>
          <LinkButton onPress={() => router.push("/(auth)/login")}>
            <LinkText>{t("intro.have_account")}</LinkText>
          </LinkButton>
        </MiddleContainer>
      </ContentContainer>
    </Container>
  );
}
