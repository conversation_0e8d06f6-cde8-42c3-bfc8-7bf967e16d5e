import { useCallback } from "react";
import { useAppDispatch, useAppSelector, RootState } from "@/store/store";
import {
  addToCartAction,
  clearCartAction,
  getCartListAction,
  placeOrderAction,
} from "@/store/actions/order";
import {
  clearCartLocal,
  updateCartItemQuantityLocal,
} from "@/store/slices/orderSlice";
import Toast from "react-native-toast-message";
import { router } from "expo-router";
import { OrderType, PlaceOrderPayloadType } from "@/types/order";
import { PaymentMethod } from "@/types/payment";
import * as ImagePicker from "expo-image-picker";
import { objectToFormdata, removeNullish } from "@/utils/common";

/**
 * useCart - Custom hook for cart operations
 * Provides cart state and actions for managing cart items
 */
export const useCart = () => {
  const dispatch = useAppDispatch();
  const { cartList, couponCode } = useAppSelector(
    (state: RootState) => state.orders
  );

  const fetchCartList = useCallback(async () => {
    try {
      await dispatch(getCartListAction({})).unwrap();
    } catch (error) {
      console.error("Failed to fetch cart list:", error);
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to load cart",
      });
    }
  }, [dispatch]);

  const updateQuantity = useCallback(
    async (productId: number, newQuantity: number) => {
      const item = cartList?.cartItems.find((i) => i.product_id === productId);
      if (!item) return;
      dispatch(
        updateCartItemQuantityLocal({ id: productId, quantity: newQuantity })
      );
    },
    [dispatch, cartList]
  );

  const clearCart = useCallback(async () => {
    if (!cartList?.cartItems?.length) return;

    try {
      const response = await dispatch(clearCartAction({})).unwrap();
      dispatch(clearCartLocal());
      Toast.show({
        type: "success",
        text1: response.message || "Cart cleared successfully",
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to clear cart",
      });
    }
  }, [dispatch, cartList]);

  const placeOrder = useCallback(
    async (
      vendorId: number | null,
      billingAddressId: number | null,
      shippingAddressId: number | null,
      paymentMethod: PaymentMethod,
      paymentProof: ImagePicker.ImagePickerAsset | null,
      selectedPaymentDetails: any
    ) => {
      if (!billingAddressId) {
        Toast.show({
          type: "error",
          text1: "Please select addresses to continue",
        });
        return false;
      }
      if (!shippingAddressId) {
        Toast.show({
          type: "error",
          text1: "Please select a shipping address to continue.",
        });
        return false;
      }

      const rawPayload: Partial<PlaceOrderPayloadType> = {
        billing_address_id: billingAddressId,
        shipping_address_id: shippingAddressId,
        order_type: OrderType.Direct,
        payment_type: paymentMethod,
        vendor_id: vendorId,
        coupon_code: couponCode,
        ...selectedPaymentDetails,
      };

      const payload: PlaceOrderPayloadType = removeNullish(
        rawPayload
      ) as PlaceOrderPayloadType;
      try {
        const formData = objectToFormdata(payload);

        if (paymentProof) {
          formData.append("payment_proof", {
            uri: paymentProof.uri,
            name: paymentProof.fileName,
            type: paymentProof.mimeType,
          } as any);
        }
        const response = await dispatch(placeOrderAction(formData)).unwrap();
        if (response.status) {
          Toast.show({
            type: "success",
            text1: response.message || "Order placed successfully",
          });
          router.push("/(protected)/(tabs)/orders");
        }
      } catch (error) {
        console.error("error", error);
        Toast.show({
          type: "error",
          text1: error?.message || "Please try again",
        });
        return false;
      }
    },
    [dispatch, couponCode]
  );

  return {
    cartList,
    fetchCartList,
    updateQuantity,
    clearCart,
    placeOrder,
  };
};
