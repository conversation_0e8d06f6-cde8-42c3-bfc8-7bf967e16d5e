import { useState, useEffect } from 'react';
import { store } from '@/store/store';

export const useStoreReady = () => {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const unsubscribe = store.subscribe(() => {
      const state = store.getState();
      // Check if the store has been rehydrated by checking if auth state exists
      if (state.auth !== undefined) {
        setIsReady(true);
        unsubscribe();
      }
    });

    // Check initial state
    const initialState = store.getState();
    if (initialState.auth !== undefined) {
      setIsReady(true);
      unsubscribe();
    }

    return unsubscribe;
  }, []);

  return isReady;
}; 