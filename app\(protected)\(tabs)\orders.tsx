import React, { useEffect, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import {
  Container,
  Content,
  Card,
  Title,
  Label,
  StatusBadge,
  StatusText,
  EmptyContainer,
  EmptyText,
  ProductItem,
  ProductImage,
  ProductInfo,
  ProductName,
  ProductQuantity,
  ProductsContainer,
  ShowMoreContainer,
  ShowMoreText,
  TotalContainer,
  TotalLabel,
  TotalValue,
  StyledProductImage,
  InfoRow,
  ItemsCountText,
  SortButtonRow,
  SortLabel,
  SortValueText,
  Header,
  ActiveFiltersContainer,
  FilterChipsRow,
  ActiveFilterChip,
  ActiveFilterText,
} from "@/styles/Order.styles";
import { LoadingOverlay, SearchBar } from "@/components";
import StatusFilterBottomSheet from "@/components/organisms/StatusFilterBottomSheet";
import { Ionicons } from "@expo/vector-icons";
import { FlatList } from "react-native";
import { Order, OrderListPayloadType } from "@/types/order";
import SortBottomSheet, {
  SortOptionList,
} from "@/components/organisms/SortBottomSheet";
import { orderSortOptions, orderTypeOptions } from "@/utils/common";
import { getOrdersListAction } from "@/store/actions/order";
import { useAppDispatch, useAppSelector, RootState } from "@/store/store";
import Toast from "react-native-toast-message";
import { useFocusEffect, useRouter } from "expo-router";
import { useTheme } from "@/hooks/useTheme";
import { setFilters } from "@/store/slices/orderSlice";

const OrdersScreen = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { theme } = useTheme();
  // Redux state
  const {
    orders,
    ordersLoading,
    orderStatusList,
    filters,
    current_page,
    last_page,
  } = useAppSelector((state: RootState) => state.orders);
  // Local state
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<number | null>(null);
  const [selectedOrderType, setSelectedOrderType] = useState<number | null>(
    null
  );
  const [search, setSearch] = useState("");
  const [sortSheetVisible, setSortSheetVisible] = useState(false);
  const [statusSheetVisible, setStatusSheetVisible] = useState(false);
  const [selectedSort, setSelectedSort] = useState<SortOptionList | null>(null);

  // Fetch orders list from API
  const fetchOrders = useCallback(
    async (params: OrderListPayloadType) => {
      try {
        if (params.page === 1) setIsRefreshing(true);
        else setIsLoadingMore(true);
        await dispatch(getOrdersListAction({ ...filters, ...params })).unwrap();
      } catch (error: any) {
        Toast.show({
          type: "error",
          text1: error?.message || "Failed to load orders",
        });
      } finally {
        setIsRefreshing(false);
        setIsLoadingMore(false);
      }
    },
    [dispatch]
  );

  // On mount, load first page
  useEffect(() => {
    const payload: OrderListPayloadType = {
      page: 1,
    };
    if (selectedStatus) payload.status = selectedStatus;
    if (selectedOrderType) payload.order_type = selectedOrderType;
    if (selectedSort?.order_by) {
      payload.order_by = selectedSort.order_by;
    }
    if (selectedSort?.sort_order) {
      payload.sort_order = selectedSort.sort_order;
    }
    fetchOrders(payload);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useFocusEffect(
    useCallback(() => {
      handleRefresh();
    }, [])
  );

  // Handlers
  const handleRefresh = useCallback(() => {
    fetchOrders({
      page: 1,
    });
    setSelectedOrderType(null);
    setSelectedSort(null);
    setSelectedStatus(null);
  }, [fetchOrders]);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && current_page < last_page) {
      fetchOrders({
        page: current_page + 1,
        order_by: selectedSort?.order_by,
        sort_order: selectedSort?.sort_order,
      });
    }
  }, [isLoadingMore, current_page, last_page, fetchOrders, selectedSort]);

  const handleSortPress = () => setSortSheetVisible(true);
  const handleStatusPress = () => setStatusSheetVisible(true);

  const handleSortClear = () => {
    setSelectedSort(null);
    setSortSheetVisible(false);
    fetchOrders({
      page: 1,
      order_by: null,
      sort_order: null,
    });
  };
  const handleSortDismiss = () => setSortSheetVisible(false);

  const handleSortApply = (sortType: SortOptionList | null) => {
    setSelectedSort(sortType);

    setSortSheetVisible(false);
    fetchOrders({
      page: 1,
      order_by: sortType?.order_by,
      sort_order: sortType?.sort_order,
    });
  };

  const handleStatusClear = () => {
    handleStatusApply(null, null);
  };
  const handleStatusApply = async (
    statusId: number | null,
    orderTypeId: number | null
  ) => {
    const payload: OrderListPayloadType = {
      page: 1,
    };
    if (selectedSort?.order_by) {
      payload.order_by = selectedSort.order_by;
    }
    if (selectedSort?.sort_order) {
      payload.sort_order = selectedSort.sort_order;
    }
    if (statusId) payload.status = statusId;
    if (orderTypeId) payload.order_type = orderTypeId;
    await dispatch(setFilters({ ...filters, ...payload }));
    setSelectedStatus(statusId);
    setSelectedOrderType(orderTypeId);
    setStatusSheetVisible(false);
    await fetchOrders(payload);
  };
  const handleStatusDismiss = () => setStatusSheetVisible(false);

  const navigateToOrderDetails = (orderId: string | number) => {
    router.push(`/order/${orderId}`);
  };

  const getOrderStatusLabel = (statusId: string | number): string => {
    return (
      orderStatusList?.find((status) => status.id === Number(statusId))
        ?.label || ""
    );
  };

  const getFormattedOrderNumber = (orderNumber: string | number): string => {
    return orderNumber.toString();
  };

  const renderOrderItem = ({ item: order }: { item: Order }) => {
    const maxVisibleProducts = 2;
    const hasMoreProducts = order.order_detail.length > maxVisibleProducts;
    const visibleProducts = hasMoreProducts
      ? order.order_detail.slice(0, maxVisibleProducts)
      : order.order_detail;
    const remainingCount = order.order_detail.length - maxVisibleProducts;

    return (
      <Card key={order.id} onPress={() => navigateToOrderDetails(order.id)}>
        <Title>{getFormattedOrderNumber(order.order_number)}</Title>
        <Label>{t("order.status")}</Label>
        <StatusBadge status={Number(order.status)}>
          <StatusText status={Number(order.status)}>
            {getOrderStatusLabel(order.status)}
          </StatusText>
        </StatusBadge>
        <Label>{t("products")}</Label>
        <ProductsContainer>
          {visibleProducts.map((item, index) => (
            <ProductItem key={index}>
              <ProductImage>
                <StyledProductImage
                  source={{ uri: item?.product?.main_image?.url || "" }}
                />
              </ProductImage>
              <ProductInfo>
                <ProductName>{item?.product?.title || ""}</ProductName>
                <ProductQuantity>
                  {t("quantity")}: {item.quantity}
                </ProductQuantity>
              </ProductInfo>
            </ProductItem>
          ))}
          {hasMoreProducts && (
            <ShowMoreContainer>
              <ShowMoreText>
                +{remainingCount} more {remainingCount === 1 ? 'item' : 'items'}
              </ShowMoreText>
            </ShowMoreContainer>
          )}
        </ProductsContainer>
        <TotalContainer>
          <TotalLabel>{t("total")}</TotalLabel>
          <TotalValue>₹{order.total_amount}</TotalValue>
        </TotalContainer>
      </Card>
    );
  };

  const renderActiveFilters = () => {
    const activeFilters = [];

    if (selectedStatus) {
      const status = orderStatusList.find((s) => s.id === selectedStatus);
      if (status) {
        activeFilters.push({
          label: status.label,
          onRemove: () => handleStatusApply(null, selectedOrderType),
        });
      }
    }

    if (selectedOrderType) {
      const orderType = orderTypeOptions.find(
        (o) => o.id === selectedOrderType
      );
      if (orderType) {
        activeFilters.push({
          label: orderType.label,
          onRemove: () => handleStatusApply(selectedStatus, null),
        });
      }
    }

    if (activeFilters.length === 0) return null;

    return (
      <ActiveFiltersContainer>
        <FilterChipsRow>
          {activeFilters.map((filter, index) => (
            <ActiveFilterChip key={index} onPress={filter.onRemove}>
              <ActiveFilterText>{filter.label}</ActiveFilterText>
              <Ionicons name="close" size={14} color={theme.colors.white} />
            </ActiveFilterChip>
          ))}
        </FilterChipsRow>
      </ActiveFiltersContainer>
    );
  };

  if (ordersLoading && !isRefreshing && !isLoadingMore && current_page === 1) {
    return <LoadingOverlay isLoading={ordersLoading} />;
  }
  return (
    <Container>
      <Header>
        <SearchBar
          onSearch={setSearch}
          onFilterPress={handleStatusPress}
          value={search}
        />
      </Header>

      {/* Info Row */}
      <InfoRow>
        <ItemsCountText>{orders?.length || 0} items</ItemsCountText>
        <SortButtonRow hitSlop={20} onPress={handleSortPress}>
          <SortLabel>{t("common.sort_by")} : </SortLabel>
          <SortValueText>{selectedSort?.label || t("common.default")}</SortValueText>
          <Ionicons
            name="chevron-down"
            size={16}
            color={theme.colors.primary}
          />
        </SortButtonRow>
      </InfoRow>

      {renderActiveFilters()}
      <Content>
        <FlatList
          data={orders}
          renderItem={renderOrderItem}
          keyExtractor={(order) => String(order.id)}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 16 }}
          onRefresh={handleRefresh}
          refreshing={isRefreshing}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            isLoadingMore ? <LoadingOverlay isLoading /> : null
          }
          ListEmptyComponent={
            <EmptyContainer>
              <EmptyText>{t("order.no_orders")}</EmptyText>
            </EmptyContainer>
          }
        />
      </Content>
      {/* Sort Bottom Sheet */}
      <SortBottomSheet
        isVisible={sortSheetVisible}
        onClose={handleSortDismiss}
        onApply={handleSortApply}
        onDismiss={handleSortDismiss}
        onClear={handleSortClear}
        sortOptions={orderSortOptions}
        selectedOptions={selectedSort}
      />
      {/* Status Filter Bottom Sheet */}
      <StatusFilterBottomSheet
        filters={filters}
        isVisible={statusSheetVisible}
        onDismiss={handleStatusDismiss}
        statusOptions={orderStatusList}
        orderTypeOptions={orderTypeOptions}
        onClear={handleStatusClear}
        onApply={handleStatusApply}
      />
    </Container>
  );
};

export default OrdersScreen;
