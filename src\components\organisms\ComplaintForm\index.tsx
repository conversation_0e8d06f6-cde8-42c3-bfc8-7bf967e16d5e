import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>View } from "react-native";
import { router } from "expo-router";
import { useTranslation } from "react-i18next";
import { FormProvider } from "react-hook-form";
import Toast from "react-native-toast-message";
import {
  FormContainer,
  FormContent,
  StepperContainer,
  StepContent,
  NavigationButtons,
  NavButton,
  NavButtonText,
} from "@/styles/Complaint.styles";
import Header from "@/components/organisms/Header";
import EntitySelectionBottomSheet from "@/components/organisms/EntitySelectionBottomSheet";
import { ComplaintStep1 } from "@/components/organisms/ComplaintStep1";
import { ComplaintStep2 } from "@/components/organisms/ComplaintStep2";
import ComplaintStep3 from "@/components/organisms/ComplaintStep3";
import { LocationStatusIndicator } from "@/components/molecules/LocationStatusIndicator";
import { VendorDetails } from "@/types/vendor";
import Stepper from "@/components/molecules/Stepper";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import { Customer } from "@/types/customer";
import { CountryCode } from "@/types/auth";
import AddManuallyForm from "@/components/organisms/AddManuallyForm";
import { useComplaintForm } from "@/hooks/useComplaintForm";
import { useLocation } from "@/hooks/useLocation";
import { useComplaintFormHandlers } from "@/hooks/useComplaintFormHandlers";
import { useComplaintValidation } from "@/hooks/useComplaintValidation";
import { useComplaintSubmission } from "@/hooks/useComplaintSubmission";
import { UserType } from "@/types/api";

export default function ComplaintForm() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // Custom hooks
  const {
    shouldSkipStep1,
    initialStep,
    stepData,
    methods,
    watch,
    setValue,
    handleSubmit,
    complaintFor,
    batteryBrand,
    selectedVendor,
    selectedCustomer,
    selectedIssue,
    customDescription,
    selectedBillingAddressId,
    selectedShippingAddressId,
    useBillingAsShipping,
    isStep1Valid,
    isStep2Valid,
    isStep3Valid,
  } = useComplaintForm();
  const { getCurrentLocation, isLoading: isLocationLoading } = useLocation();
  const { user } = useAppSelector((state: RootState) => state.auth);
  // Local state
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [showEntitySheet, setShowEntitySheet] = useState(false);
  const [showAddManuallyForm, setShowAddManuallyForm] = useState(false);
  const [isLoadingVendors, setIsLoadingVendors] = useState(false);
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(false);
  const [isLoadingQRCode, setIsLoadingQRCode] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [locationCoordinates, setLocationCoordinates] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [isLocationRequested, setIsLocationRequested] = useState(false);

  // Redux state
  const { customers } = useAppSelector((state: RootState) => state.customer);
  const { vendorList } = useAppSelector((state: RootState) => state.vendor);

  // Custom hooks for form logic
  const { validateStep1, validateStep2, validateFinalSubmission } =
    useComplaintValidation({
      complaintFor,
      selectedVendor,
      selectedCustomer,
      selectedIssue,
      customDescription,
      batteryBrand,
      selectedBillingAddressId,
      selectedShippingAddressId,
      useBillingAsShipping,
      watch,
    });

  const {
    handleEntitySelect,
    handleAddManuallyForm,
    handleComplaintTypeChange,
    handleBatteryBrandChange,
    handleQRCodeChange,
  } = useComplaintFormHandlers({
    complaintFor,
    setValue,
    setShowAddManuallyForm,
    dispatch,
  });

  const { onSubmit } = useComplaintSubmission({
    isSubmitting,
    setIsSubmitting,
    locationCoordinates,
    setLocationCoordinates,
    getCurrentLocation,
    dispatch,
    selectedIssue,
    customDescription,
    watch,
    router,
  });

  // Location management
  useEffect(() => {
    const getLocationOnFocus = async () => {
      if (isLocationRequested) return;

      setIsLocationRequested(true);

      try {
        const coordinates = await getCurrentLocation();
        if (coordinates) {
          setLocationCoordinates(coordinates);
          setLocationError(null);
        } else {
          setLocationError("Failed to get location coordinates");
        }
      } catch (error) {
        console.error("Error getting location on focus:", error);
        setLocationError("Error getting location coordinates");
      }
    };

    getLocationOnFocus();
  }, [getCurrentLocation, isLocationRequested]);

  const retryLocation = async () => {
    setIsLocationRequested(false);
    setLocationError(null);
    setLocationCoordinates(null);
  };

  // Navigation handlers
  const handleNextStep = () => {
    if (currentStep === 1) {
      if (!validateStep1()) return;
      setCurrentStep(2);
    } else if (currentStep === 2) {
      if (!validateStep2()) return;
      setCurrentStep(3);
    }
  };

  const handlePrevStep = () => {
    if (currentStep === 3) {
      setCurrentStep(2);
    } else if (currentStep === 2) {
      if (shouldSkipStep1) {
        router.back();
      } else {
        setCurrentStep(1);
      }
    } else {
      router.back();
    }
  };

  const handleStepNavigation = (stepId: number) => {
    if (stepId <= currentStep) {
      setCurrentStep(stepId);
      return;
    }

    if (stepId > currentStep) {
      if (currentStep === 1 && !validateStep1()) {
        return;
      }
      if (currentStep === 2 && !validateStep2()) {
        return;
      }
      setCurrentStep(stepId);
    }
  };

  // Event handlers
  const handleSearchPress = () => setShowEntitySheet(true);
  const handleAddManual = () => setShowAddManuallyForm(true);

  // Helper functions
  const getCurrentStepValidation = () => {
    switch (currentStep) {
      case 1:
        return isStep1Valid();
      case 2:
        return isStep2Valid();
      case 3:
        return isStep3Valid();
      default:
        return false;
    }
  };

  const getStepValidation = (stepId: number) => {
    switch (stepId) {
      case 1:
        return isStep1Valid();
      case 2:
        return isStep2Valid();
      case 3:
        return isStep3Valid();
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return !shouldSkipStep1 ? (
          <StepContent>
            <ComplaintStep1
              complaintFor={complaintFor}
              selectedVendor={selectedVendor}
              selectedCustomer={selectedCustomer}
              mobileNumber={
                complaintFor === "vendor"
                  ? watch("vendorMobileNumber")
                  : watch("customerMobileNumber")
              }
              onComplaintTypeChange={handleComplaintTypeChange}
              onAddManually={handleAddManual}
              onSearchPress={handleSearchPress}
            />
            <AddManuallyForm
              isVisible={showAddManuallyForm}
              onClose={() => setShowAddManuallyForm(false)}
              onAdd={handleAddManuallyForm}
              entityType={complaintFor}
            />
          </StepContent>
        ) : null;

      case 2:
        return (
          <StepContent>
            <ComplaintStep2
              batteryBrand={batteryBrand}
              serialNumber={watch("serialNumber")}
              qrCode={watch("qrCode")}
              productImages={watch("productImages") || []}
              brandName={watch("brandName")}
              description={watch("description")}
              selectedIssue={selectedIssue}
              isLoadingQRCode={isLoadingQRCode}
              onBatteryBrandChange={handleBatteryBrandChange}
              onSerialNumberChange={(value) => setValue("serialNumber", value)}
              onQRCodeChange={handleQRCodeChange}
              onProductImagesChange={(images) =>
                setValue("productImages", images)
              }
              onBrandNameChange={(value) => setValue("brandName", value)}
              onDescriptionChange={(value) => setValue("description", value)}
              onIssueSelect={(issueId) => setValue("selectedIssue", issueId)}
            />
          </StepContent>
        );

      case 3:
        return (
          <StepContent>
            <ComplaintStep3
              selectedBillingAddressId={selectedBillingAddressId}
              selectedShippingAddressId={selectedShippingAddressId}
              useBillingAsShipping={useBillingAsShipping}
              setSelectedBillingAddressId={(id) =>
                setValue("selectedBillingAddressId", id)
              }
              setSelectedShippingAddressId={(id) =>
                setValue("selectedShippingAddressId", id)
              }
              setUseBillingAsShipping={(value) =>
                setValue("useBillingAsShipping", value)
              }
              selectedVendor={selectedVendor}
              selectedCustomer={selectedCustomer}
            />
          </StepContent>
        );

      default:
        return null;
    }
  };

  return (
    <FormProvider {...methods}>
      <Header
        title={t("complaint.new_complaint")}
        onBackPress={() => router.back()}
        showCart={false}
      />

      <StepperContainer>
        <Stepper
          stepData={stepData}
          currentId={currentStep}
          setSelectedTabNav={handleStepNavigation}
          allowFutureStepNavigation={false}
          getStepValidation={getStepValidation}
        />
      </StepperContainer>

      <FormContainer>
        <LocationStatusIndicator
          isLoading={isLocationLoading}
          error={locationError}
          coordinates={locationCoordinates}
          onRetry={retryLocation}
        />

        <ScrollView showsVerticalScrollIndicator={false}>
          <FormContent>{renderStepContent()}</FormContent>
        </ScrollView>
      </FormContainer>

      <NavigationButtons>
        <NavButton
          variant="secondary"
          onPress={handlePrevStep}
          disabled={isSubmitting || isLocationLoading}
        >
          <NavButtonText variant="secondary">
            {currentStep === 1 && !shouldSkipStep1
              ? t("complaint.navigation.back")
              : t("complaint.navigation.previous")}
          </NavButtonText>
        </NavButton>

        {currentStep < 3 ? (
          <NavButton
            style={{ flex: 1 }}
            variant="primary"
            onPress={handleNextStep}
            disabled={!getCurrentStepValidation()}
          >
            <NavButtonText
              variant="primary"
              disabled={!getCurrentStepValidation()}
            >
              {t("complaint.navigation.next")}
            </NavButtonText>
          </NavButton>
        ) : (
          <NavButton
            style={{ flex: 1 }}
            variant="primary"
            onPress={handleSubmit(onSubmit)}
            disabled={isSubmitting || isLocationLoading}
          >
            <NavButtonText
              variant="primary"
              disabled={isSubmitting || isLocationLoading}
            >
              {isSubmitting || isLocationLoading
                ? t("common.loading")
                : t("complaint.submit")}
            </NavButtonText>
          </NavButton>
        )}
      </NavigationButtons>

      {user?.role_id === UserType.SALESPERSON && (
        <EntitySelectionBottomSheet
          isVisible={showEntitySheet}
          onClose={() => setShowEntitySheet(false)}
          onDismiss={() => setShowEntitySheet(false)}
          onSelect={handleEntitySelect}
          onAddManual={handleAddManual}
          entityType={complaintFor}
          entities={
            complaintFor === "vendor" ? vendorList || [] : customers || []
          }
          isLoading={
            complaintFor === "vendor" ? isLoadingVendors : isLoadingCustomers
          }
          selectedEntity={
            complaintFor === "vendor" ? selectedVendor : selectedCustomer
          }
        />
      )}
    </FormProvider>
  );
}
